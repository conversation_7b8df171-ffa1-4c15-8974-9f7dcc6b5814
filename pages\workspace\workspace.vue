<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="nav-bar">
			<view class="status-bar"></view>
			<view class="header">
				<text class="title">工作台</text>
			</view>
		</view>

		<!-- 页面内容区域 -->
		<view class="page-content">
			<!-- 功能网格 -->
			<view class="function-grid">
				<!-- 第一行 -->
				<view class="grid-row">
					<view class="grid-item" @tap="navigateTo('/subpages/search/search')">
						<view class="icon-wrapper">
							<image class="icon" src="/static/icons/search.svg" mode="aspectFit"></image>
						</view>
						<text class="label">搜索场地</text>
					</view>
					<view class="grid-item" @tap="navigateTo('/subpages/add-location/add-location')">
						<view class="icon-wrapper">
							<image class="icon" src="/static/icons/add.svg" mode="aspectFit"></image>
						</view>
						<text class="label">提交场地</text>
					</view>
					<view class="grid-item" @tap="navigateTo('/subpages/favorites/favorites')">
						<view class="icon-wrapper">
							<image class="icon" src="/static/icons/heart.svg" mode="aspectFit"></image>
						</view>
						<text class="label">我的收藏</text>
					</view>
					<view class="grid-item" @tap="navigateTo('/subpages/my-locations/my-locations')">
						<view class="icon-wrapper">
							<image class="icon" src="/static/icons/location-marker.svg" mode="aspectFit"></image>
						</view>
						<text class="label">我的场地</text>
					</view>
				</view>

				<!-- 第二行 -->
				<view class="grid-row">
					<view class="grid-item" @tap="navigateTo('/subpages/settings/settings')">
						<view class="icon-wrapper">
							<image class="icon" src="/static/icons/settings.svg" mode="aspectFit"></image>
						</view>
						<text class="label">设置</text>
					</view>
					<view class="grid-item" @tap="navigateTo('/subpages/about/about')">
						<view class="icon-wrapper">
							<image class="icon" src="/static/icons/info.svg" mode="aspectFit"></image>
						</view>
						<text class="label">关于我们</text>
					</view>
					<view class="grid-item" @tap="navigateTo('/subpages/feedback/feedback')">
						<view class="icon-wrapper">
							<image class="icon" src="/static/icons/feedback.svg" mode="aspectFit"></image>
						</view>
						<text class="label">意见反馈</text>
					</view>
					<view class="grid-item" @tap="navigateTo('/subpages/help/help')">
						<view class="icon-wrapper">
							<image class="icon" src="/static/icons/help.svg" mode="aspectFit"></image>
						</view>
						<text class="label">帮助中心</text>
					</view>
				</view>

				<!-- 第三行 -->
				<view class="grid-row">
					<view class="grid-item" @tap="navigateToTab('/pages/nearby/nearby')">
						<view class="icon-wrapper">
							<image class="icon" src="/static/tabbar/fj.png" mode="aspectFit"></image>
						</view>
						<text class="label">附近场地</text>
					</view>
					<view class="grid-item" @tap="navigateToTab('/pages/index/index')">
						<view class="icon-wrapper">
							<image class="icon" src="/static/tabbar/home.png" mode="aspectFit"></image>
						</view>
						<text class="label">首页</text>
					</view>
					<view class="grid-item" @tap="navigateToTab('/pages/sports-event/sports-event')">
						<view class="icon-wrapper">
							<image class="icon" src="/static/tabbar/yueqiu.png" mode="aspectFit"></image>
						</view>
						<text class="label">约球</text>
					</view>
					<view class="grid-item" @tap="navigateToTab('/pages/profile/profile')">
						<view class="icon-wrapper">
							<image class="icon" src="/static/tabbar/me.png" mode="aspectFit"></image>
						</view>
						<text class="label">我的</text>
					</view>
				</view>
			</view>

			<!-- 快捷操作区域 -->
			<view class="quick-actions">
				<view class="section-title">快捷操作</view>
				<view class="action-list">
					<view class="action-item" @tap="quickAddLocation">
						<view class="action-icon">
							<text class="icon-text">+</text>
						</view>
						<view class="action-content">
							<text class="action-title">快速发布场地</text>
							<text class="action-desc">一键发布运动场地信息</text>
						</view>
						<view class="action-arrow">
							<image src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
						</view>
					</view>

					<view class="action-item" @tap="quickSearch">
						<view class="action-icon">
							<text class="icon-text">🔍</text>
						</view>
						<view class="action-content">
							<text class="action-title">快速搜索</text>
							<text class="action-desc">搜索附近运动场地</text>
						</view>
						<view class="action-arrow">
							<image src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
						</view>
					</view>

					<view class="action-item" @tap="goToMyEvents">
						<view class="action-icon">
							<text class="icon-text">⚽</text>
						</view>
						<view class="action-content">
							<text class="action-title">我的约球</text>
							<text class="action-desc">查看我参与的约球活动</text>
						</view>
						<view class="action-arrow">
							<image src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
						</view>
					</view>

					<view class="action-item" @tap="goToGroupChats">
						<view class="action-icon">
							<text class="icon-text">💬</text>
							<view class="unread-badge" v-if="totalUnreadCount > 0">
								<text class="unread-count">{{ totalUnreadCount > 99 ? '99+' : totalUnreadCount }}</text>
							</view>
						</view>
						<view class="action-content">
							<text class="action-title">群聊消息</text>
							<text class="action-desc" v-if="totalUnreadCount > 0">{{ totalUnreadCount }}条未读消息</text>
							<text class="action-desc" v-else>查看群聊消息</text>
						</view>
						<view class="action-arrow">
							<image src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Mixins from "../../minix/index.js"
import imManager from "../../utils/IMManager.js"

export default {
	mixins: [Mixins],
	data() {
		return {
			totalUnreadCount: 0,
			conversations: [],
			updateTimer: null
		}
	},

	mounted() {
		this.initNavBar()

		// 注册IM登录成功回调
		imManager.onLoginSuccess(this.onIMLoginSuccess)

		// 注册SDK ready回调
		imManager.onReady(this.onSDKReady)

		// 监听全局事件，当用户进行约球操作时更新未读消息数
		uni.$on('refreshUnreadMessages', this.loadUnreadMessages)

		// 如果IM已经登录且SDK已ready，直接加载未读消息
		this.$nextTick(() => {
			const loginStatus = imManager.getLoginStatus()
			if (loginStatus.isLoggedIn && imManager.isSDKReady) {
				this.loadUnreadMessages()
			}
		})
	},

	onShow() {
		// 页面显示时检查IM登录状态和SDK状态后再刷新未读消息数
		const loginStatus = imManager.getLoginStatus()
		if (loginStatus.isLoggedIn && imManager.isSDKReady) {
			// 延迟一下再加载，确保其他页面的操作已完成
			setTimeout(() => {
				this.loadUnreadMessages()
			}, 500)
		}

		// 注册消息更新监听
		this.registerMessageListener()
	},

	onHide() {
		// 页面隐藏时移除监听
		this.removeMessageListener()
	},

	onUnload() {
		// 页面卸载时移除监听
		this.removeMessageListener()

		// 移除IM登录成功回调
		imManager.offLoginSuccess(this.onIMLoginSuccess)

		// 移除SDK ready回调
		imManager.offReady(this.onSDKReady)

		// 移除全局事件监听
		uni.$off('refreshUnreadMessages', this.loadUnreadMessages)
	},
	
	methods: {
		// 初始化导航栏
		initNavBar() {
			this.$nextTick(() => {
				const res = uni.getMenuButtonBoundingClientRect()
				const statusHeight = res.top
				const navHeight = res.height
				this.top = statusHeight + navHeight + 10
			})
		},

		// IM登录成功回调
		onIMLoginSuccess() {
			console.log('IM登录成功，检查SDK状态后加载未读消息')
			// 只有在SDK也ready的情况下才加载未读消息
			if (imManager.isSDKReady) {
				this.loadUnreadMessages()
			}
		},

		// SDK ready回调
		onSDKReady() {
			console.log('SDK ready，检查登录状态后加载未读消息')
			// 只有在已登录的情况下才加载未读消息
			const loginStatus = imManager.getLoginStatus()
			if (loginStatus.isLoggedIn) {
				this.loadUnreadMessages()
			}
		},

		// 加载未读消息数
		async loadUnreadMessages() {

			try {
				// 检查用户是否已登录
				const token = uni.getStorageSync('ydToken')
				if (!token || !this.$store.state.userinfo.nickname) {
					this.totalUnreadCount = 0
					return
				}

				// 检查IM登录状态和SDK状态
				const loginStatus = imManager.getLoginStatus()
				if (!loginStatus.isLoggedIn) {
					console.log('IM未登录，无法获取未读消息')
					this.totalUnreadCount = 0
					return
				}

				if (!imManager.isSDKReady) {
					console.log('SDK未ready，无法获取未读消息')
					this.totalUnreadCount = 0
					return
				}

				// 获取未读消息数
				const result = await imManager.getTotalUnreadCount()
				if (result.success) {
					console.log("未读消息",result);
					
					this.totalUnreadCount = result.count
					this.conversations = result.conversations || []
					console.log('成功获取未读消息数:', this.totalUnreadCount)
				} else {
					console.log('获取未读消息数失败:', result.error)
					this.totalUnreadCount = 0
				}
			} catch (error) {
				console.error('加载未读消息数失败:', error)
				this.totalUnreadCount = 0
			}
		},
		
		// 导航到普通页面
		navigateTo(url) {
			// 检查是否需要登录
			if (this.needLogin(url) && !this.$store.state.userinfo.nickname) {
				uni.reLaunch({
					url: "/pages/profile/profile?flag=1"
				});
				return;
			}
			
			uni.navigateTo({
				url: url,
				fail(){
					uni.showToast({
						title: '正在开发中！',
						icon: 'none'
					})
				}
			})
		},
		
		// 导航到 tabBar 页面
		navigateToTab(url) {
			if (this.needLogin(url) && !this.$store.state.userinfo.nickname) {
				uni.reLaunch({
					url: "/pages/profile/profile?flag=1"
				});
				return;
			}
			uni.switchTab({
				url: url
			})
		},
		
		// 检查是否需要登录
		needLogin(url) {
			const loginRequiredPages = [
				'/pages/sports-event/sports-event',
				'/subpages/add-location/add-location',
				'/subpages/my-locations/my-locations'
			];
			return loginRequiredPages.includes(url);
		},
		
		// 快速发布场地
		quickAddLocation() {
			this.navigateTo('/subpages/add-location/add-location')
		},
		
		// 快速搜索
		quickSearch() {
			this.navigateTo('/subpages/search/search')
		},

		// 注册消息监听
		registerMessageListener() {
			// 监听IM消息更新
			if (typeof imManager !== 'undefined' && imManager.onNewMessage) {
				imManager.onNewMessage = this.onMessageUpdate
			}
		},

		// 移除消息监听
		removeMessageListener() {
			if (typeof imManager !== 'undefined') {
				imManager.onNewMessage = () => {}
			}
		},

		// 消息更新回调
		onMessageUpdate(event) {
			console.log('收到消息更新事件:', event)

			// 延迟更新未读消息数，避免频繁调用
			if (this.updateTimer) {
				clearTimeout(this.updateTimer)
			}
			this.updateTimer = setTimeout(() => {
				this.loadUnreadMessages()
			}, 1000)
		},

		// 跳转到我的约球
		goToMyEvents() {
			// 检查是否需要登录
			if (!this.$store.state.userinfo.nickname) {
				uni.reLaunch({
					url: "/pages/profile/profile?flag=1"
				});
				return;
			}

			// 跳转到约球页面并切换到我的约球标签
			uni.switchTab({
				url: '/pages/sports-event/sports-event',
				success: () => {
					// 通过事件通知约球页面切换到我的约球
					uni.$emit('switchToMyEvents')
				}
			})
		},

		// 跳转到群聊列表
		goToGroupChats() {
			// 检查是否需要登录
			if (!this.$store.state.userinfo.nickname) {
				uni.reLaunch({
					url: "/pages/profile/profile?flag=1"
				});
				return;
			}

			// 检查IM登录状态
			const loginStatus = imManager.getLoginStatus()
			if (!loginStatus.isLoggedIn) {
				uni.showToast({
					title: 'IM未登录，请稍后重试',
					icon: 'none'
				})
				return
			}

			// 如果没有群聊
			if (this.conversations.length === 0) {
				uni.showToast({
					title: '暂无群聊',
					icon: 'none'
				})
				return
			}

			// 直接跳转到群聊列表页面
			uni.navigateTo({
				url: '/subpages/group-list/group-list'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
}

.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background: #4CAF50;
}

.header {
	padding: 20rpx 30rpx;
	text-align: center;
	position: relative;

	.title {
		color: #fff;
		font-size: 36rpx;
		font-weight: 500;
	}
}

.page-content {
	padding-top: calc(var(--status-bar-height) + 88rpx);
	flex: 1;
	padding: calc(var(--status-bar-height) + 88rpx) 30rpx 30rpx;
}

.function-grid {
	background: #fff;
	border-radius: 16rpx;
	padding: 40rpx 20rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.grid-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 40rpx;
	
	&:last-child {
		margin-bottom: 0;
	}
}

.grid-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx 10rpx;
	
	.icon-wrapper {
		width: 80rpx;
		height: 80rpx;
		background: #f8f9fa;
		border-radius: 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 16rpx;
		
		.icon {
			width: 40rpx;
			height: 40rpx;
		}
	}
	
	.label {
		font-size: 24rpx;
		color: #333;
		text-align: center;
	}
}

.quick-actions {
	background: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 30rpx;
}

.action-list {
	.action-item {
		display: flex;
		align-items: center;
		padding: 24rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
		
		&:last-child {
			border-bottom: none;
		}
		
		.action-icon {
			width: 80rpx;
			height: 80rpx;
			background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
			border-radius: 16rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 24rpx;
			position: relative;

			.icon-text {
				font-size: 32rpx;
				color: #fff;
				font-weight: bold;
			}

			.unread-badge {
				position: absolute;
				top: -8rpx;
				right: -8rpx;
				background: #ff4757;
				border-radius: 20rpx;
				min-width: 32rpx;
				height: 32rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border: 2rpx solid #fff;

				.unread-count {
					font-size: 20rpx;
					color: #fff;
					font-weight: bold;
					padding: 0 6rpx;
				}
			}
		}
		
		.action-content {
			flex: 1;
			
			.action-title {
				font-size: 30rpx;
				color: #333;
				font-weight: 500;
				display: block;
				margin-bottom: 8rpx;
			}
			
			.action-desc {
				font-size: 24rpx;
				color: #666;
			}
		}
		
		.action-arrow {
			width: 24rpx;
			height: 24rpx;
		}
	}
}

/* 状态栏适配 */
.status-bar {
	height: var(--status-bar-height);
	width: 100%;
}
</style>
